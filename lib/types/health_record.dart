import 'package:flutter/material.dart';
import 'package:health_diary/types/health_types.dart';

/// 健康记录条目
class HealthRecordEntry {
  final int id;
  final DateTime time;
  final String note;
  final HealthRecordTypeEnum recordType;
  final List<HealthRecordValue> values;
  final int color;

  const HealthRecordEntry({
    required this.id,
    required this.time,
    required this.note,
    required this.recordType,
    required this.values,
    required this.color,
  });
}

/// 健康记录数值
class HealthRecordValue {
  final String label;
  final String value;
  final String unit;
  final bool isAbnormal;

  const HealthRecordValue({
    required this.label,
    required this.value,
    required this.unit,
    this.isAbnormal = false,
  });
}

class TodayHealthOverview {
  final String? value;
  final String unit;
  final IconData icon;
  final Color iconColor;
  final bool isAbnormal;

  const TodayHealthOverview({
    required this.value,
    required this.unit,
    required this.icon,
    required this.iconColor,
    this.isAbnormal = false,
  });
}
