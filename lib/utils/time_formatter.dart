import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';

/// 时间格式化工具类
/// 提供智能的时间显示格式
class TimeFormatter {
  /// 格式化时间显示
  /// 
  /// 规则：
  /// - 今天：显示 "今天 HH:mm"
  /// - 昨天：显示 "昨天 HH:mm"  
  /// - 今年其他日期：显示 "MM-dd HH:mm"
  /// - 非今年日期：显示 "yyyy-MM-dd HH:mm"
  /// 
  /// [dateTime] 要格式化的时间
  /// 返回格式化后的时间字符串
  static String formatDateTime(DateTime dateTime, Locale locale) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final targetDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    // 格式化时间部分 HH:mm
    final timeFormat = DateFormat('HH:mm');
    final timeString = timeFormat.format(dateTime);
    
    if (targetDate == today) {
      // 今天
      return '${'today'.tr()} $timeString';
    } else if (targetDate == yesterday) {
      // 昨天
      return '${'yesterday'.tr()} $timeString';
    } else if (dateTime.year == now.year) {
      // 今年其他日期
      final dateFormat = DateFormat.MMMd(locale.languageCode);
      final dateString = dateFormat.format(dateTime);
      return '$dateString $timeString';
    } else {
      // 非今年日期
      final dateFormat = DateFormat.yMMMd(locale.languageCode);
      final dateString = dateFormat.format(dateTime);
      return '$dateString $timeString';
    }
  }

  
  /// 检查是否为今天
  static bool isToday(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
    return targetDate == today;
  }
  
  /// 检查是否为昨天
  static bool isYesterday(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final targetDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
    return targetDate == yesterday;
  }
  
  /// 检查是否为今年
  static bool isThisYear(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year;
  }
}
