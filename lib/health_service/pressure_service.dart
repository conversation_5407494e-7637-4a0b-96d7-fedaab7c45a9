import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_record.dart';
import 'package:health_diary/types/health_types.dart';

import 'health_service.dart';
import 'widgets/blood_pressure_form.dart';

class PressureService extends HealthService {
  @override
  bool canScan() => true;

  @override
  HealthRecordEntry parseRecordEntry(HealthRecord record) {
    final pressure = record.data as BloodPressureData;

    final values = <HealthRecordValue>[
      HealthRecordValue(
        label: 'systolic_label'.tr(),
        value: pressure.systolic.toString(),
        unit: 'mmHg',
        isAbnormal: _isSystolicAbnormal(pressure.systolic),
      ),
      HealthRecordValue(
        label: 'diastolic_label'.tr(),
        value: pressure.diastolic.toString(),
        unit: 'mmHg',
        isAbnormal: _isDiastolicAbnormal(pressure.diastolic),
      ),
    ];

    if (pressure.pulse != null) {
      values.add(HealthRecordValue(
        label: 'pulse_label'.tr(),
        value: pressure.pulse.toString(),
        unit: 'bpm',
        isAbnormal: _isPulseAbnormal(pressure.pulse!),
      ));
    }

    return HealthRecordEntry(
      id: record.id,
      time: record.createdAt,
      note: pressure.note ?? "",
      recordType: HealthRecordTypeEnum.bloodPressure,
      values: values,
      color: 0xFFFFEBF0, // 绿色
    );
  }

  @override
  TodayHealthOverview calculateAverage(List<HealthRecord> records) {
    if (records.isEmpty) {
      return emptyOverview();
    }

    double totalSystolic = 0;
    double totalDiastolic = 0;

    for (final record in records) {
      final pressure = record.data as BloodPressureData;
      totalSystolic += pressure.systolic;
      totalDiastolic += pressure.diastolic;
    }

    final avgSystolic = (totalSystolic / records.length).toInt();
    final avgDiastolic = (totalDiastolic / records.length).toInt();
    final isAbnormal = _isSystolicAbnormal(avgSystolic) || _isDiastolicAbnormal(avgDiastolic);

    return TodayHealthOverview(
      value:"$avgSystolic/$avgDiastolic",
      unit: 'mmHg',
      icon: FontAwesomeIcons.heartPulse,
      iconColor: Colors.pink.withValues(alpha: 0.6),
      isAbnormal: isAbnormal,
    );
  }

  @override
  TodayHealthOverview emptyOverview() {
    return TodayHealthOverview(
      value: "--",
      unit: "mmHg",
      icon: FontAwesomeIcons.heartPulse,
      iconColor: Colors.pink.withValues(alpha: 0.6)
    );
  }

  @override
  Widget buildInputForm({required GlobalKey<FormState> formKey}) {
    return BloodPressureForm(formKey: formKey);
  }

  @override
  HealthRecordData? parseScannerData(List<int> data) {
    if (data.length < 2) {
      return null;
    }

    List<int> remainingData = List.from(data);
    int? systolic;
    int? diastolic;
    int? pulse;

    // 匹配高压 (收缩压，正常范围约90-200)
    for (int i = 0; i < remainingData.length; i++) {
      int value = remainingData[i];
      if (value >= 50 && value <= 200) {
        systolic = value;
        remainingData.removeAt(i);
        break;
      }
    }

    if (systolic == null) {
      return null;
    }

    // 匹配低压 (舒张压，正常范围约60-120)
    for (int i = 0; i < remainingData.length; i++) {
      int value = remainingData[i];
      if (value >= 30 && value <= 120 && value < systolic) {
        diastolic = value;
        remainingData.removeAt(i);
        break;
      }
    }

    if (diastolic == null) {
      return null;
    }

    // 匹配脉搏 (可选，正常范围约50-150)
    if (remainingData.isNotEmpty) {
      for (int i = 0; i < remainingData.length; i++) {
        int value = remainingData[i];
        if (value >= 40 && value <= 220) {
          pulse = value;
          break;
        }
      }
    }

    return HealthRecordData.bloodPressure(
      systolic: systolic,
      diastolic: diastolic,
      pulse: pulse,
      createdAt: DateTime.now()
    );
  }

  /// 判断收缩压是否异常
  /// 根据2024年中国高血压防治指南：收缩压 >= 130 mmHg 为异常
  bool _isSystolicAbnormal(int systolic) {
    return systolic >= 130;
  }

  /// 判断舒张压是否异常
  /// 根据2024年中国高血压防治指南：舒张压 >= 85 mmHg 为异常
  bool _isDiastolicAbnormal(int diastolic) {
    return diastolic >= 85;
  }

  /// 判断脉搏是否异常
  /// 正常成人静息心率：60-100 bpm
  bool _isPulseAbnormal(int pulse) {
    return pulse < 60 || pulse > 100;
  }
}
